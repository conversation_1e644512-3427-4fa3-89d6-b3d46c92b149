package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type CMSProjectOption func(repository.IRepository[models.CMSProject])

var CMSProject = func(c core.IContext, options ...CMSProjectOption) repository.IRepository[models.CMSProject] {
	r := repository.New[models.CMSProject](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func CMSProjectOrderBy(pageOptions *core.PageOptions) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CMSProjectByStatus(statuses []string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if len(statuses) > 0 {
			c.Where("status IN ?", statuses)
		}
	}
}

func CMSProjectByType(types []string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if len(types) > 0 {
			c.Where("type IN ?", types)
		}
	}
}

func CMSProjectByMinistry(ministryID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if ministryID != "" {
			c.Where("ministry_id = ?", ministryID)
		}
	}
}

func CMSProjectByDepartment(departmentID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if departmentID != "" {
			c.Where("department_id = ?", departmentID)
		}
	}
}

func CMSProjectByDivision(divisionID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if divisionID != "" {
			c.Where("division_id = ?", divisionID)
		}
	}
}

func CMSProjectBySearch(query string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if query != "" {
			c.Where("name ILIKE ? OR domain ILIKE ? OR contact_name ILIKE ? OR contact_email ILIKE ?",
				"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
		}
	}
}

func CMSProjectByPhaseDateRange(startDate, endDate string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if startDate != "" || endDate != "" {
			// Join with cms_project_phases table to filter by current phases only
			c.Joins("JOIN cms_project_phases ON cms_projects.id = cms_project_phases.cms_project_id")

			// Filter for current phases (start_date <= now AND end_date >= now)
			c.Where("cms_project_phases.start_date <= NOW() AND cms_project_phases.end_date >= NOW()")

			// Additional date range filtering if provided
			if startDate != "" {
				c.Where("cms_project_phases.start_date >= ?", startDate)
			}
			if endDate != "" {
				c.Where("cms_project_phases.end_date <= ?", endDate)
			}

			// Use DISTINCT to avoid duplicate projects when multiple phases match
			c.Distinct("cms_projects.*")
		}
	}
}

func CMSProjectByPhase(phase string, workPhase string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if phase != "" || workPhase != "" {
			c.Joins("JOIN cms_project_phases ON cms_projects.id = cms_project_phases.cms_project_id")
			if phase != "" {
				c.Where("cms_project_phases.phase = ?", phase)
			}
			if workPhase != "" {
				c.Where("cms_project_phases.work_phase = ?", workPhase)
			}
			c.Distinct("cms_projects.*")
		}
	}
}

func CMSProjectWithRelations() CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		c.Preload("Ministry").
			Preload("Department").
			Preload("Division").
			Preload("Phase").
			Preload("CreatedBy").
			Preload("UpdatedBy")

	}
}
